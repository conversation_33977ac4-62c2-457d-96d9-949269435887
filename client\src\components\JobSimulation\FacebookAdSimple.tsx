import { useEffect, useRef, useState } from 'react';
import { useSetRecoilState } from 'recoil';
import { SAInputField, SimulationAppScreen } from '~/common';
import {
  Input,
  Label,
  OGDialog,
  OGDialogContent,
  OGDialogFooter,
  OGDialogHeader,
  OGDialogTitle,
} from '~/components';
import store from '~/store';
import { cn } from '~/utils';

// LineChart component for rendering charts
interface LineChartProps {
  data: {
    datasets: Array<{ x: number; y: number; label: string; time: number }>;
    chartArea: {
      left: number;
      top: number;
      width: number;
      height: number;
    };
  };
}

const LineChart: React.FC<LineChartProps> = ({ data }) => {
  const { datasets, chartArea } = data;
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [visiblePoints, setVisiblePoints] = useState<number>(0);
  const [startTime, setStartTime] = useState<number | null>(null);

  // Start timer when component mounts
  useEffect(() => {
    setStartTime(Date.now());
    setElapsedTime(0);
  }, []);

  // Timer for real-time updates
  useEffect(() => {
    if (startTime === null) return;

    const interval = setInterval(() => {
      const currentTime = Date.now();
      const elapsed = Math.floor((currentTime - startTime) / 1000); // Convert to seconds
      setElapsedTime(elapsed);
    }, 1000); // Update every second for more accurate timing

    return () => clearInterval(interval);
  }, [startTime]);

  // Update visible points based on elapsed time
  useEffect(() => {
    const newVisiblePoints = datasets.filter((point) => point.time <= elapsedTime).length;
    setVisiblePoints(newVisiblePoints);
  }, [elapsedTime, datasets]);

  // Get currently visible datasets
  const currentDatasets = datasets.slice(0, visiblePoints);

  // Create SVG path from visible datasets
  const createPath = () => {
    if (currentDatasets.length === 0) return '';

    // Convert absolute percentage coordinates to relative coordinates within the chart area
    const points = currentDatasets.map((point) => ({
      x: ((point.x - chartArea.left) / chartArea.width) * 100,
      y: ((point.y - chartArea.top) / chartArea.height) * 100,
    }));

    let path = `M ${points[0].x} ${points[0].y}`;
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`;
    }

    return path;
  };

  return (
    <div
      style={{
        position: 'absolute',
        left: `${chartArea.left}%`,
        top: `${chartArea.top}%`,
        width: `${chartArea.width}%`,
        height: `${chartArea.height}%`,
        zIndex: 15,
        pointerEvents: 'auto',
      }}
    >
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 100 100"
        preserveAspectRatio="none"
        style={{
          overflow: 'visible',
        }}
      >
        {/* Chart line */}
        {currentDatasets.length > 1 && (
          <path
            d={createPath()}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="2"
            vectorEffect="non-scaling-stroke"
            style={{
              filter:
                hoveredPoint !== null ? 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.5))' : 'none',
              transition: 'filter 0.2s ease',
            }}
          />
        )}

        {/* Data points */}
        {currentDatasets.map((point, index) => {
          const x = ((point.x - chartArea.left) / chartArea.width) * 100;
          const y = ((point.y - chartArea.top) / chartArea.height) * 100;

          return (
            <g key={index}>
              <circle
                cx={x}
                cy={y}
                r={hoveredPoint === index ? '1' : '0.8'}
                fill="#3b82f6"
                stroke="white"
                strokeWidth="2"
                vectorEffect="non-scaling-stroke"
                style={{
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  filter:
                    hoveredPoint === index
                      ? 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.8))'
                      : 'none',
                  opacity: index === currentDatasets.length - 1 ? 1 : 0.8, // Highlight newest point
                }}
                onMouseEnter={() => setHoveredPoint(index)}
                onMouseLeave={() => setHoveredPoint(null)}
              />

              {/* Tooltip on hover */}
              {hoveredPoint === index && (
                <g>
                  {/* <rect
                    x={x + 1}
                    y={y - 10}
                    width="10"
                    height="10"
                    fill="rgba(0, 0, 0, 0.8)"
                    vectorEffect="non-scaling-stroke"
                  /> */}
                  <text
                    x={x + 2}
                    y={y - 2}
                    fill="rgba(0, 0, 0, 0.8)"
                    fontSize="3"
                    textAnchor="middle"
                    vectorEffect="non-scaling-stroke"
                  >
                    {point.label}
                  </text>
                </g>
              )}
            </g>
          );
        })}
      </svg>

      <div
        style={{
          position: 'absolute',
          top: '-20px',
          left: '0',
          fontSize: '10px',
          color: '#666',
          background: 'rgba(255,255,255,0.8)',
          padding: '2px 4px',
          borderRadius: '2px',
        }}
      >
        {/* Time: {elapsedTime}s | Points: {visiblePoints}/{datasets.length} */}
        Time: {elapsedTime}s
      </div>
    </div>
  );
};

// Shared dropdown options for budget selection
const budgetDropdownOptions = [
  {
    label: '$2.00',
    screenId: '005',
    dataContextId: 'budget',
    dataContext:
      'Budget: $2.00. VAT: $0.10. Estimated number of audience each day: 2.1K - 6.1K. Estimated likes each day: 0 - 125.',
    saveToSelections: true,
  },
  {
    label: '$5.00',
    screenId: '005_02',
    dataContextId: 'budget',
    dataContext:
      'Budget: $5.00. VAT: $0.25. Estimated number of audience each day: 5.3K - 15.3K. Estimated likes each day: 108 - 312.',
    saveToSelections: true,
  },
  {
    label: '$20.00',
    screenId: '005_03',
    dataContextId: 'budget',
    dataContext:
      'Budget: $20.00. VAT: $1.00. Estimated number of audience each day: 21.1K - 60.9K. Estimated likes each day: 416 - 1.2K.',
    saveToSelections: true,
  },
  {
    label: '$100.00',
    screenId: '005_04',
    dataContextId: 'budget',
    dataContext:
      'Budget: $100.00. VAT: $5.00. Estimated number of audience each day: 95.8K - 276.9K. Estimated likes each day: 803 - 2.3K.',
    saveToSelections: true,
  },
  {
    label: '$500.00',
    screenId: '005_05',
    dataContextId: 'budget',
    dataContext:
      'Budget: $500.00. VAT: $25.00. Estimated number of audience each day: 479.2K - 1.4M. Estimated likes each day: 4.0K - 11.6K.',
    saveToSelections: true,
  },
];

const screens: SimulationAppScreen[] = [
  {
    id: '001',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/001.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Create Ad',
        x1: 55.4,
        y1: 43.25,
        x2: 59.2,
        y2: 46.2,
        left: 55.4,
        top: 43.25,
        width: 3.8,
        height: 2.75,
        action: { type: 'nextScreen' },
      },
      {
        title: 'Create Ad',
        x1: 95.3,
        y1: 1.3,
        x2: 99.2,
        y2: 4.3,
        left: 95.2,
        top: 1.57,
        width: 4.0,
        height: 2.36,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '002',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/002.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Choose a goal:',
        x1: 62.2,
        y1: 12.3,
        x2: 69.8,
        y2: 30.97,
        left: 62.2,
        top: 12.54,
        width: 7.6,
        height: 18.43,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '003',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/003.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Create post',
        x1: 46.8,
        y1: 40.5,
        x2: 51.1,
        y2: 43.5,
        left: 46.8,
        top: 40.75,
        width: 4.2,
        height: 2.35,
        action: { type: 'nextScreen' },
      },
    ],
  },
  {
    id: '004',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/004.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Add photo',
        x1: 5.64,
        y1: 38.99,
        x2: 12.76,
        y2: 43.06,
        left: 5.64,
        top: 38.99,
        width: 7.12,
        height: 4.07,
        action: { type: 'uploadPhoto' },
      },
      {
        title: "Post's text",
        x1: 5.64,
        y1: 55.86,
        x2: 38.72,
        y2: 72.45,
        left: 5.64,
        top: 55.86,
        width: 33.08,
        height: 16.59,
        action: {
          type: 'inputText',
          inputType: 'textarea',
          dataContextId: 'postText',
          saveToSelections: true,
        },
      },
      {
        title: 'Publish',
        x1: 33.92,
        y1: 92.39,
        x2: 38.68,
        y2: 96.52,
        left: 33.87,
        top: 92.39,
        width: 4.81,
        height: 4.13,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: { type: 'nextScreen' },
      },
      {
        title: 'Button (unnamed)',
        x1: 22.58,
        y1: 92.54,
        x2: 26.99,
        y2: 96.54,
        left: 22.58,
        top: 92.54,
        width: 4.41,
        height: 4.0,
        action: { type: 'nextScreen', screenId: '003' },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        x1: 5.57,
        y1: 30.7,
        x2: 8.43,
        y2: 36.31,
        title: 'Image small placeholder',
      },
      {
        id: 'imageLarge',
        type: 'image',
        x1: 55.15,
        y1: 31.6,
        x2: 84.2,
        y2: 88.4,
        title: 'Image large placeholder',
      },
      {
        id: 'text',
        type: 'text',
        x1: 56.14,
        y1: 22.02,
        x2: 82.93,
        y2: 29.81,
        title: 'Text placeholder',
      },
    ],
  },
  {
    id: '005',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget1.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Budget',
        x1: 8.01,
        y1: 19.35,
        x2: 32.66,
        y2: 25.31,
        left: 8.01,
        top: 19.35,
        width: 24.65,
        height: 5.96,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Publish Ad',
        x1: 93.1,
        y1: 95.55,
        x2: 97.25,
        y2: 99.2,
        left: 93.0,
        top: 95.57,
        width: 4.09,
        height: 3.87,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          type: 'triggerMessage',
          message: "I've published the ad with the following settings:",
          withData: true,
        },
      },
      {
        title: 'Cancel',
        x1: 88.87,
        y1: 95.5,
        x2: 92.7,
        y2: 99.08,
        left: 88.87,
        top: 95.76,
        width: 3.74,
        height: 3.32,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
  },
  {
    id: '005_02',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget2.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Budget',
        x1: 8.01,
        y1: 19.35,
        x2: 32.66,
        y2: 25.31,
        left: 8.01,
        top: 19.35,
        width: 24.65,
        height: 5.96,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Publish Ad',
        x1: 93.0,
        y1: 95.57,
        x2: 97.3,
        y2: 99.44,
        left: 93.0,
        top: 95.57,
        width: 4.09,
        height: 3.87,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          type: 'triggerMessage',
          message: "I've published the ad with the following settings:",
          withData: true,
        },
      },
      {
        title: 'Cancel',
        x1: 88.87,
        y1: 95.76,
        x2: 92.61,
        y2: 99.08,
        left: 88.87,
        top: 95.76,
        width: 3.74,
        height: 3.32,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
  },
  {
    id: '005_03',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget3.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Budget',
        x1: 8.01,
        y1: 19.35,
        x2: 32.66,
        y2: 25.31,
        left: 8.01,
        top: 19.35,
        width: 24.65,
        height: 5.96,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Publish Ad',
        x1: 93.0,
        y1: 95.57,
        x2: 97.3,
        y2: 99.44,
        left: 93.0,
        top: 95.57,
        width: 4.09,
        height: 3.87,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          type: 'triggerMessage',
          message: "I've published the ad with the following settings:",
          withData: true,
        },
      },
      {
        title: 'Cancel',
        x1: 88.87,
        y1: 95.76,
        x2: 92.61,
        y2: 99.08,
        left: 88.87,
        top: 95.76,
        width: 3.74,
        height: 3.32,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
  },
  {
    id: '005_04',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget4.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Budget',
        x1: 8.01,
        y1: 19.35,
        x2: 32.66,
        y2: 25.31,
        left: 8.01,
        top: 19.35,
        width: 24.65,
        height: 5.96,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Publish Ad',
        x1: 93.0,
        y1: 95.57,
        x2: 97.3,
        y2: 99.44,
        left: 93.0,
        top: 95.57,
        width: 4.09,
        height: 3.87,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          // type: 'triggerMessage',
          // message: "I've published the ad with the following settings:",
          // withData: true,
          type: 'nextScreen',
          screenId: '006',
        },
      },
      {
        title: 'Cancel',
        x1: 88.87,
        y1: 95.76,
        x2: 92.61,
        y2: 99.08,
        left: 88.87,
        top: 95.76,
        width: 3.74,
        height: 3.32,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
  },
  {
    id: '005_05',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/005_budget5.png',
    bgColor: 'bg-[#64767e]',
    buttons: [
      {
        title: 'Budget',
        x1: 8.01,
        y1: 19.35,
        x2: 32.66,
        y2: 25.31,
        left: 8.01,
        top: 19.35,
        width: 24.65,
        height: 5.96,
        action: {
          type: 'dropdown',
          dropdownOptions: budgetDropdownOptions,
        },
      },
      {
        title: 'Publish Ad',
        x1: 93.0,
        y1: 95.57,
        x2: 97.3,
        y2: 99.44,
        left: 93.0,
        top: 95.57,
        width: 4.09,
        height: 3.87,
        backgroundColor: 'rgba(22, 217, 97, 0.5)',
        border: 'rgba(22, 217, 97, 0.8)',
        action: {
          // type: 'triggerMessage',
          // message: "I've published the ad with the following settings:",
          // withData: true,
          type: 'nextScreen',
          screenId: '006',
        },
      },
      {
        title: 'Cancel',
        x1: 88.87,
        y1: 95.76,
        x2: 92.61,
        y2: 99.08,
        left: 88.87,
        top: 95.76,
        width: 3.74,
        height: 3.32,
        action: { type: 'nextScreen', screenId: '004' },
      },
    ],
  },
  {
    id: '006',
    title: '',
    image: '/assets/job-simulation/facebook-ads/simple/006.png',
    bgColor: 'bg-[#64767e]',
    charts: [
      {
        id: 'chart1',
        type: 'line',
        data: {
          datasets: [
            { x: 10.04, y: 67.41, label: '10k', time: 6 },
            { x: 14.38, y: 65.8, label: '15k', time: 9 },
            { x: 19.18, y: 63.73, label: '50k', time: 12 },
            { x: 24.12, y: 61.89, label: '60k', time: 15 },
            { x: 28.31, y: 60.97, label: '70k', time: 24 },
            { x: 35.8, y: 60.28, label: '75k', time: 48 },
            { x: 37.3, y: 54.99, label: '300k', time: 60 },
            { x: 46.44, y: 53.15, label: '400k', time: 90 },
            { x: 55.88, y: 52.0, label: '500k', time: 120 },
          ],
          chartArea: {
            left: 3.89,
            top: 43.25,
            width: 55.73,
            height: 29.22,
          },
        },
      },
    ],
    placeholders: [
      {
        id: 'imageSmall',
        type: 'image',
        x1: 5.57,
        y1: 30.7,
        x2: 8.43,
        y2: 36.31,
        title: 'Image small placeholder',
      },
    ],
  },
];

const getClickedButton = (
  screen: SimulationAppScreen,
  clickXPercent: number,
  clickYPercent: number,
) => {
  if (!screen.buttons?.length) return;
  for (const button of screen.buttons) {
    const { x1, y1, x2, y2 } = button;
    if (clickXPercent >= x1 && clickXPercent <= x2 && clickYPercent >= y1 && clickYPercent <= y2) {
      return button;
    }
  }
  return null; // No button matched
};

const getClickedInput = (
  screen: SimulationAppScreen,
  clickXPercent: number,
  clickYPercent: number,
) => {
  if (!screen.inputs?.length) return;
  for (const input of screen.inputs) {
    if (input.clickable && input.x1 && input.y1 && input.x2 && input.y2) {
      if (
        clickXPercent >= input.x1 &&
        clickXPercent <= input.x2 &&
        clickYPercent >= input.y1 &&
        clickYPercent <= input.y2
      ) {
        return input;
      }
    }
  }
  return null; // No input matched
};

const getNonClickableInputs = (screen: SimulationAppScreen) => {
  if (!screen.inputs?.length) return [];
  return screen.inputs.filter((input) => !input.clickable);
};

const FacebookAdSimple = () => {
  const imageRef: React.MutableRefObject<HTMLImageElement | null> = useRef(null);
  const containerRef: React.MutableRefObject<HTMLDivElement | null> = useRef(null);
  const imageContainerRef = useRef<HTMLDivElement | null>(null);

  const [currentScreenIndex, setCurrentScreenIndex] = useState(0);
  const [showDialog, setShowDialog] = useState(false);
  const [dialogInputs, setDialogInputs] = useState<SAInputField[]>([]);
  const [inputValues, setInputValues] = useState<Record<string, string>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showDropdown, setShowDropdown] = useState(false);
  const [dropdownOptions, setDropdownOptions] = useState<
    Array<{
      label: string;
      screenId: string;
      dataContext?: string;
      dataContextId?: string;
      saveToSelections?: boolean;
    }>
  >([]);
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 });
  const [userSelections, setUserSelections] = useState<Record<string, string>>({});

  // State for screen content (images and text)
  const [screenContent, setScreenContent] = useState<
    Record<
      string,
      {
        uploadedImage?: string;
        inputText?: string;
      }
    >
  >({});

  // State for text input dialog
  const [showTextDialog, setShowTextDialog] = useState(false);
  const [textInputValue, setTextInputValue] = useState('');
  const [textInputType, setTextInputType] = useState<'input' | 'textarea'>('input');
  const [currentTextDataContextId, setCurrentTextDataContextId] = useState<string | undefined>();
  const [currentTextSaveToSelections, setCurrentTextSaveToSelections] = useState(false);

  const setJobsimulationTriggerMessage = useSetRecoilState(store.jobSimulationTriggerMessage);

  // Reset input values and errors when screen changes
  useEffect(() => {
    if (screens[currentScreenIndex].triggerMessage)
      setJobsimulationTriggerMessage({
        message: screens[currentScreenIndex].triggerMessage,
        isTriggered: true,
      });

    setInputValues({});
    setValidationErrors({});
    setShowDialog(false);
    setShowDropdown(false);
  }, [currentScreenIndex]);

  // Function to find screen index by ID
  const findScreenIndexById = (screenId: string) => {
    return screens.findIndex((screen) => screen.id === screenId);
  };

  // Handle photo upload
  const handlePhotoUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const imageUrl = event.target?.result as string;
          // Crop image to 240x240
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 240;
            canvas.height = 240;

            if (ctx) {
              // Calculate crop dimensions to maintain aspect ratio
              const size = Math.min(img.width, img.height);
              const x = (img.width - size) / 2;
              const y = (img.height - size) / 2;

              ctx.drawImage(img, x, y, size, size, 0, 0, 240, 240);
              const croppedImageUrl = canvas.toDataURL();

              setScreenContent((prev) => ({
                ...prev,
                [screens[currentScreenIndex].id]: {
                  ...prev[screens[currentScreenIndex].id],
                  uploadedImage: croppedImageUrl,
                },
              }));

              // Note: Photo upload doesn't save to userSelections as per requirement
            }
          };
          img.src = imageUrl;
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  // Handle text input
  const handleTextInput = (
    inputType: 'input' | 'textarea' = 'input',
    dataContextId?: string,
    saveToSelections?: boolean,
  ) => {
    setTextInputValue(screenContent[screens[currentScreenIndex].id]?.inputText || '');
    setTextInputType(inputType);
    setCurrentTextDataContextId(dataContextId);
    setCurrentTextSaveToSelections(saveToSelections || false);
    setShowTextDialog(true);
  };

  // Handle text dialog submit
  const handleTextDialogSubmit = () => {
    setScreenContent((prev) => ({
      ...prev,
      [screens[currentScreenIndex].id]: {
        ...prev[screens[currentScreenIndex].id],
        inputText: textInputValue,
      },
    }));

    // Update user selections only if saveToSelections is true
    if (currentTextSaveToSelections && currentTextDataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [currentTextDataContextId]: `Text: "${textInputValue}"`,
      }));
    }

    setShowTextDialog(false);
    setTextInputValue('');
  };

  const handleInputChange = (id: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [id]: value }));
    if (validationErrors[id]) {
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  // Validate inputs
  const validateInputs = () => {
    const errors: Record<string, string> = {};
    let isValid = true;

    dialogInputs.forEach((input) => {
      const value = inputValues[input.id] || '';
      if (value !== input.expectedValue) {
        errors[input.id] = `Invalid value. Expected: ${input.expectedValue}`;
        isValid = false;
      }
    });

    setValidationErrors(errors);
    return isValid;
  };

  const handleDialogSubmit = () => {
    if (validateInputs()) {
      setShowDialog(false);
      setInputValues({});
      // Move to next screen
      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
    } else {
      // TODO: Show error
      console.log('Validation failed:', validationErrors);
    }
  };

  useEffect(() => {
    function handleResize() {
      if (imageRef.current && containerRef.current) {
        // const naturalWidth = imageRef.current.naturalWidth;
        // const naturalHeight = imageRef.current.naturalHeight;

        // const containerWidth = containerRef.current.clientWidth ?? 0;
        const containerHeight = containerRef.current.clientHeight ?? 0;

        // const scale = Math.min(containerWidth / naturalWidth, containerHeight / naturalHeight);

        // const scaledWidth = naturalWidth * scale;
        // const scaledHeight = naturalHeight * scale;

        imageRef.current.style.width = `auto`;
        imageRef.current.style.maxHeight = `${containerHeight}px`;

        if (imageContainerRef.current) {
          imageContainerRef.current.style.width = `auto`;
          imageContainerRef.current.style.maxHeight = `${containerHeight}px`;
        }
      }
    }

    handleResize();
  }, [imageRef.current?.src, containerRef?.current?.clientWidth, imageContainerRef]);

  const handleClickButton = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!imageRef.current) return;

    const rect = imageRef.current.getBoundingClientRect();
    const clickXPercent = ((e.clientX - rect.left) / rect.width) * 100;
    const clickYPercent = ((e.clientY - rect.top) / rect.height) * 100;

    const currentScreen = screens[currentScreenIndex];
    const screenButton = getClickedButton(currentScreen, clickXPercent, clickYPercent);
    const clickedInput = getClickedInput(currentScreen, clickXPercent, clickYPercent);

    // If click a button
    if (screenButton) {
      if (screenButton.action.type === 'nextScreen') {
        if (screenButton.action.screenId) {
          const targetIndex = findScreenIndexById(screenButton.action.screenId);
          if (targetIndex !== -1) {
            setCurrentScreenIndex(targetIndex);
          }
        } else {
          setCurrentScreenIndex((prevIndex) => prevIndex + 1);
        }
      } else if (screenButton.action.type === 'dropdown' && screenButton.action.dropdownOptions) {
        // Show dropdown
        setDropdownOptions(screenButton.action.dropdownOptions);
        setDropdownPosition({
          x: (screenButton.x1 + screenButton.x2) / 2,
          y: screenButton.y2 + 2,
        });
        setShowDropdown(true);
      } else if (screenButton.action.type === 'uploadPhoto') {
        // Handle photo upload
        handlePhotoUpload();
      } else if (screenButton.action.type === 'inputText') {
        // Handle text input
        handleTextInput(
          screenButton.action.inputType,
          screenButton.action.dataContextId,
          screenButton.action.saveToSelections,
        );
      } else if (screenButton.action.type === 'triggerMessage') {
        // Handle trigger message
        const message = screenButton.action.message || '';
        let finalMessage = message;

        if (screenButton.action.withData && Object.keys(userSelections).length > 0) {
          const selectionsText = Object.entries(userSelections)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');
          finalMessage = `${message} ${selectionsText}`;
        }

        console.log('finalMessage ::: 1 ', finalMessage);

        setJobsimulationTriggerMessage({
          message: finalMessage,
          isTriggered: true,
        });
      }
      return;
    }

    // If click an input
    if (clickedInput) {
      setDialogInputs([clickedInput]);
      setShowDialog(true);
      return;
    }

    // If the screen was clicked (not on a button or input)
    if (!screenButton && !clickedInput) {
      const nonClickableInputs = getNonClickableInputs(currentScreen);
      if (nonClickableInputs.length > 0) {
        setDialogInputs(nonClickableInputs);
        setShowDialog(true);
        return;
      }
    }

    // If the screen has a default action (like auto-advancing)
    if (currentScreen.actions?.[0]?.type === 'nextScreen') {
      setCurrentScreenIndex((prevIndex) => prevIndex + 1);
    }
  };

  const handleDropdownOptionClick = (
    screenId: string,
    optionLabel: string,
    dataContext?: string,
    dataContextId?: string,
    saveToSelections?: boolean,
  ) => {
    const targetIndex = findScreenIndexById(screenId);
    if (targetIndex !== -1) {
      setCurrentScreenIndex(targetIndex);
    }

    // Save user selection if saveToSelections is true
    if (saveToSelections && dataContextId) {
      setUserSelections((prev) => ({
        ...prev,
        [dataContextId]: dataContext || optionLabel,
      }));
    }

    setShowDropdown(false);
  };

  useEffect(() => {
    if (imageRef.current) {
      imageRef.current.src = screens[currentScreenIndex].image;
    }
  }, [imageRef, currentScreenIndex]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
    }

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDropdown]);

  // useEffect(() => {
  //   if (screens[currentScreenIndex].triggerMessage)
  //     setJobsimulationTriggerMessage({
  //       message: screens[currentScreenIndex].triggerMessage,
  //       isTriggered: true,
  //     });
  // }, [currentScreenIndex, setJobsimulationTriggerMessage]);

  // State for tracking hover
  const [hoveredElement, setHoveredElement] = useState<{
    type: 'button' | 'input';
    index: number;
  } | null>(null);

  // Calculate positions for buttons and clickable inputs
  const getButtonStyles = (button: any): React.CSSProperties => {
    return {
      position: 'absolute' as const,
      left: `${button.x1}%`,
      top: `${button.y1}%`,
      width: `${button.x2 - button.x1}%`,
      height: `${button.y2 - button.y1}%`,
      cursor: 'pointer',
      zIndex: 10,
      backgroundColor:
        hoveredElement?.type === 'button' && hoveredElement?.index === button.index
          ? (button.backgroundColor ?? 'rgba(0, 123, 255, 0.2)')
          : 'transparent',
      border:
        hoveredElement?.type === 'button' && hoveredElement?.index === button.index
          ? (button.border ?? '2px solid rgba(0, 123, 255, 0.5)')
          : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto',
    };
  };

  const getInputStyles = (input: SAInputField, index: number): React.CSSProperties | undefined => {
    if (!input.clickable || !input.x1 || !input.y1 || !input.x2 || !input.y2) return undefined;

    return {
      position: 'absolute' as const,
      left: `${input.x1}%`,
      top: `${input.y1}%`,
      width: `${input.x2 - input.x1}%`,
      height: `${input.y2 - input.y1}%`,
      cursor: 'text',
      zIndex: 10,
      backgroundColor:
        hoveredElement?.type === 'input' && hoveredElement?.index === index
          ? 'rgba(255, 193, 7, 0.2)'
          : 'transparent',
      border:
        hoveredElement?.type === 'input' && hoveredElement?.index === index
          ? '2px solid rgba(255, 193, 7, 0.5)'
          : 'none',
      borderRadius: '4px',
      transition: 'all 0.2s ease',
      pointerEvents: 'auto', // Ensure the overlay captures mouse events
    };
  };

  return (
    <>
      <div
        className={cn(
          'flex h-full flex-col items-center justify-center',
          screens[currentScreenIndex].bgColor ? `${screens[currentScreenIndex].bgColor}` : '',
        )}
        ref={containerRef}
      >
        <div className="relative" ref={imageContainerRef}>
          <img
            ref={imageRef}
            onClick={handleClickButton}
            // className="max-h-full w-auto"
            style={{ display: 'block' }} // Ensure image is block to avoid layout issues
          />

          {/* Overlay for buttons */}
          {screens[currentScreenIndex].buttons?.map((button, index) => (
            <div
              key={`button-${index}`}
              style={getButtonStyles({ ...button, index })}
              onMouseEnter={() => setHoveredElement({ type: 'button', index })}
              onMouseLeave={() => setHoveredElement(null)}
              onClick={(e) => {
                e.stopPropagation();
                if (button.action.type === 'nextScreen') {
                  if (button.action.screenId) {
                    const targetIndex = findScreenIndexById(button.action.screenId);
                    if (targetIndex !== -1) {
                      setCurrentScreenIndex(targetIndex);
                    }
                  } else {
                    setCurrentScreenIndex((prevIndex) => prevIndex + 1);
                  }
                } else if (button.action.type === 'dropdown' && button.action.dropdownOptions) {
                  // Show dropdown
                  setDropdownOptions(button.action.dropdownOptions);
                  setDropdownPosition({
                    x: (button.x1 + button.x2) / 2,
                    y: button.y2 + 2,
                  });
                  setShowDropdown(true);
                } else if (button.action.type === 'uploadPhoto') {
                  // Handle photo upload
                  handlePhotoUpload();
                } else if (button.action.type === 'inputText') {
                  // Handle text input
                  handleTextInput(
                    button.action.inputType,
                    button.action.dataContextId,
                    button.action.saveToSelections,
                  );
                } else if (button.action.type === 'triggerMessage') {
                  // Handle trigger message
                  const message = button.action.message || '';
                  let finalMessage = message;

                  if (button.action.withData && Object.keys(userSelections).length > 0) {
                    const selectionsText = Object.entries(userSelections)
                      .map(([key, value]) => `${key}: ${value}`)
                      .join(', ');
                    finalMessage = `${message} - User selections: ${selectionsText}`;
                  }

                  console.log('finalMessage ::: 2 ', finalMessage);

                  setJobsimulationTriggerMessage({
                    message: finalMessage,
                    isTriggered: true,
                  });
                }
              }}
              title={button.title}
            />
          ))}

          {/* Overlay for inputs */}
          {screens[currentScreenIndex].inputs?.map((input, index) => {
            const style = getInputStyles(input, index);
            return input.clickable && style ? (
              <div
                key={`input-${index}`}
                style={style}
                onMouseEnter={() => setHoveredElement({ type: 'input', index })}
                onMouseLeave={() => setHoveredElement(null)}
                onClick={(e) => {
                  e.stopPropagation();
                  setDialogInputs([input]);
                  setShowDialog(true);
                }}
                title={input.label}
              />
            ) : null;
          })}

          {/* Placeholders overlay */}
          {screens[currentScreenIndex].placeholders?.map((placeholder, index) => {
            const currentContent = screenContent[screens[currentScreenIndex].id];

            if (placeholder.type === 'image' && currentContent?.uploadedImage) {
              return (
                <div
                  key={`placeholder-${index}`}
                  style={{
                    position: 'absolute',
                    left: `${placeholder.x1}%`,
                    top: `${placeholder.y1}%`,
                    width: `${placeholder.x2 - placeholder.x1}%`,
                    height: `${placeholder.y2 - placeholder.y1}%`,
                    zIndex: 15,
                    overflow: 'hidden',
                    borderRadius: '4px',
                  }}
                >
                  <img
                    src={currentContent.uploadedImage}
                    alt="Uploaded content"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                    }}
                  />
                </div>
              );
            }

            if (placeholder.type === 'text' && currentContent?.inputText) {
              return (
                <div
                  // className='text-xs'
                  key={`placeholder-${index}`}
                  style={{
                    position: 'absolute',
                    left: `${placeholder.x1}%`,
                    top: `${placeholder.y1}%`,
                    width: `${placeholder.x2 - placeholder.x1}%`,
                    height: `${placeholder.y2 - placeholder.y1}%`,
                    zIndex: 15,
                    display: 'flex',
                    justifyContent: 'left',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    borderRadius: '4px',
                    fontSize: '0.75rem',
                    lineHeight: '0.75rem',
                    padding: '4px 2px 2px 2px',
                    color: '#333',
                    textAlign: 'left',
                    overflow: 'auto',
                    wordBreak: 'break-word',
                    whiteSpace: 'pre-line',
                  }}
                >
                  {currentContent.inputText}
                </div>
              );
            }

            return null;
          })}

          {/* Charts overlay */}
          {screens[currentScreenIndex].charts?.map((chart, index) => {
            if (chart.type === 'line') {
              return <LineChart key={`chart-${index}`} data={chart.data} />;
            }
            return null;
          })}

          {/* Dropdown overlay */}
          {showDropdown && (
            <div
              style={{
                position: 'absolute',
                left: `${dropdownPosition.x}%`,
                top: `${dropdownPosition.y}%`,
                transform: 'translateX(-50%)',
                zIndex: 20,
                backgroundColor: 'white',
                border: '1px solid #ccc',
                borderRadius: '4px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                minWidth: '150px',
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {dropdownOptions.map((option, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px 12px',
                    cursor: 'pointer',
                    borderBottom: index < dropdownOptions.length - 1 ? '1px solid #eee' : 'none',
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f5f5f5';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'white';
                  }}
                  onClick={() => {
                    handleDropdownOptionClick(
                      option.screenId,
                      option.label,
                      option.dataContext,
                      option.dataContextId,
                      option.saveToSelections,
                    );
                  }}
                >
                  {option.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Dialog for dynamic inputs  */}
      <OGDialog open={showDialog} onOpenChange={setShowDialog}>
        <OGDialogContent
          title={`${screens[currentScreenIndex].title} - Input Required`}
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>
              {dialogInputs.length === 1
                ? `Enter ${dialogInputs[0]?.label}`
                : 'Please enter the required information'}
            </OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 py-4">
            {dialogInputs.map((input) => (
              <div key={input.id} className="flex flex-col gap-2">
                <Label htmlFor={input.id}>{input.label}</Label>
                <Input
                  id={input.id}
                  value={inputValues[input.id] || ''}
                  onChange={(e) => handleInputChange(input.id, e.target.value)}
                  className={cn(
                    validationErrors[input.id] ? 'border-red-500 focus:border-red-500' : '',
                    'transition-all duration-200',
                  )}
                  placeholder={`Enter ${input.label.toLowerCase()}`}
                />
                {validationErrors[input.id] && (
                  <p className="text-sm text-red-500">{validationErrors[input.id]}</p>
                )}
              </div>
            ))}
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowDialog(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleDialogSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                OK
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>

      {/* Text Input Dialog */}
      <OGDialog open={showTextDialog} onOpenChange={setShowTextDialog}>
        <OGDialogContent
          title="Enter Text"
          className="max-w-[600px] bg-background text-text-primary shadow-2xl"
        >
          <OGDialogHeader>
            <OGDialogTitle>Enter your text content</OGDialogTitle>
          </OGDialogHeader>

          <div className="flex flex-col gap-4 py-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="textInput">Text Content</Label>
              {textInputType === 'textarea' ? (
                <textarea
                  id="textInput"
                  value={textInputValue}
                  onChange={(e) => setTextInputValue(e.target.value)}
                  className="min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background transition-all duration-200 placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Enter your text here..."
                  rows={5}
                />
              ) : (
                <Input
                  id="textInput"
                  value={textInputValue}
                  onChange={(e) => setTextInputValue(e.target.value)}
                  className="transition-all duration-200"
                  placeholder="Enter your text here..."
                />
              )}
            </div>
          </div>

          <OGDialogFooter>
            <div className="flex gap-2">
              <button
                onClick={() => setShowTextDialog(false)}
                className="mt-3 block w-fit rounded bg-gray-100 px-4 py-2 text-base font-semibold text-neutral-900 hover:opacity-90"
              >
                Cancel
              </button>
              <button
                onClick={handleTextDialogSubmit}
                className="mt-3 block w-fit rounded bg-neutral-900 px-4 py-2 text-base font-semibold text-white hover:opacity-90"
              >
                OK
              </button>
            </div>
          </OGDialogFooter>
        </OGDialogContent>
      </OGDialog>
    </>
  );
};

export default FacebookAdSimple;
